import axios from "axios";
import { getBackendConfigValue } from "../config/environment";

const sendDataToHubspot = async (formPage, payload, formGuid) => {
  try {
    // Get HubSpot Portal ID from configuration
    const hubspotPortalId = getBackendConfigValue('HUBSPOT_PORTAL_ID');

    if (!hubspotPortalId) {
      throw new Error('HubSpot Portal ID not configured');
    }

    const response = await axios.post(
      `https://api.hsforms.com/submissions/v3/integration/submit/${hubspotPortalId}/${formGuid}`,
      payload
    );

    return {
      status: response.status, // Return actual HTTP status code
      message: `${formPage} form data sent to HubSpot successfully.`,
    };
  } catch (error) {
    return {
      status: error.response?.status || 500, // Return error status if available
      error: `${formPage} error while sending data to HubSpot.`,
    };
  }
};

export default sendDataToHubspot;
