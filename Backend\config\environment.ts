/**
 * Backend configuration service
 * This handles environment variables for the Backend services
 */

/**
 * Interface for backend configuration
 */
export interface BackendConfig {
  // HubSpot Configuration
  HUBSPOT_API_KEY: string;
  HUBSPOT_GET_IN_TOUCH_FORM_GUID: string;
  HUBSPOT_PORTAL_ID: string;
  HUBSPOT_AI_READINESS_FORM_GUID?: string;
  
  // Email Configuration
  MAIL_FROM: string;
  MAIL_TO: string;
  SECOND_RECIPIENT?: string;
  
  // SendGrid Configuration
  SENDGRID_API_KEY: string;
  SENDGRID_CONTACT_US_FORM_TEMPLATE_ID: string;
  SENDGRID_FAILURE_EMAIL_TEMPLATE_ID: string;
  SENDGRID_AI_READINESS_FORM_TEMPLATE_ID?: string;
  
  // Slack Configuration
  SLACK_FAILURE_WEBHOOK_URL: string;
  SLACK_SUCCESS_WEBHOOK_URL: string;
}

/**
 * Default fallback values
 */
const DEFAULT_CONFIG: Partial<BackendConfig> = {};

/**
 * Cached configuration
 */
let cachedConfig: BackendConfig | null = null;

/**
 * Load configuration from environment variables
 */
function loadFromEnvironment(): Partial<BackendConfig> {
  const config: Partial<BackendConfig> = {};
  
  // Map environment variables to config
  const envMapping: Record<string, keyof BackendConfig> = {
    'NEXT_PUBLIC_HUBSPOT_API_KEY': 'HUBSPOT_API_KEY',
    'NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID': 'HUBSPOT_GET_IN_TOUCH_FORM_GUID',
    'NEXT_PUBLIC_HUBSPOT_PORTAL_ID': 'HUBSPOT_PORTAL_ID',
    'NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID': 'HUBSPOT_AI_READINESS_FORM_GUID',
    'NEXT_PUBLIC_MAIL_FROM': 'MAIL_FROM',
    'NEXT_PUBLIC_MAIL_TO': 'MAIL_TO',
    'NEXT_PUBLIC_SECOND_RECIPENT': 'SECOND_RECIPIENT',
    'NEXT_PUBLIC_SENDGRID_API_KEY': 'SENDGRID_API_KEY',
    'NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID': 'SENDGRID_CONTACT_US_FORM_TEMPLATE_ID',
    'NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID': 'SENDGRID_FAILURE_EMAIL_TEMPLATE_ID',
    'NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID': 'SENDGRID_AI_READINESS_FORM_TEMPLATE_ID',
    'NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL': 'SLACK_FAILURE_WEBHOOK_URL',
    'NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL': 'SLACK_SUCCESS_WEBHOOK_URL',
  };
  
  for (const [envKey, configKey] of Object.entries(envMapping)) {
    const value = process.env[envKey];
    if (value) {
      (config as any)[configKey] = value;
    }
  }
  
  return config;
}

/**
 * Get backend configuration
 */
export function getBackendConfig(): BackendConfig {
  // Return cached config if available
  if (cachedConfig) {
    return cachedConfig;
  }
  
  let config: Partial<BackendConfig> = { ...DEFAULT_CONFIG };
  
  // Load from environment variables
  const envConfig = loadFromEnvironment();
  config = { ...config, ...envConfig };
  
  // Validate required parameters
  const configValues = Object.values(config).filter(Boolean);
  if (configValues.length === 0) {
    throw new Error('No configuration values found. Please check your environment variables.');
  }
  
  // Cache the configuration
  cachedConfig = config as BackendConfig;
  
  return cachedConfig;
}

/**
 * Get a specific configuration value
 */
export function getBackendConfigValue<K extends keyof BackendConfig>(key: K): BackendConfig[K] | undefined {
  try {
    const config = getBackendConfig();
    return config[key];
  } catch (error) {
    console.error(`Failed to get backend config value for '${key}':`, error);
    return undefined;
  }
}

/**
 * Clear configuration cache (useful for testing)
 */
export function clearBackendConfigCache(): void {
  cachedConfig = null;
  console.log('Backend configuration cache cleared');
}

/**
 * Validate that all required configuration is present
 */
export function validateBackendConfiguration(): boolean {
  try {
    const config = getBackendConfig();
    const requiredKeys: (keyof BackendConfig)[] = [
      'HUBSPOT_API_KEY',
      'HUBSPOT_GET_IN_TOUCH_FORM_GUID',
      'HUBSPOT_PORTAL_ID',
      'MAIL_FROM',
      'MAIL_TO',
      'SENDGRID_API_KEY',
      'SENDGRID_CONTACT_US_FORM_TEMPLATE_ID',
      'SENDGRID_FAILURE_EMAIL_TEMPLATE_ID',
      'SLACK_FAILURE_WEBHOOK_URL',
      'SLACK_SUCCESS_WEBHOOK_URL',
    ];
    
    const missingKeys = requiredKeys.filter(key => !config[key]);
    
    if (missingKeys.length > 0) {
      console.error('Missing required backend configuration keys:', missingKeys);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Backend configuration validation failed:', error);
    return false;
  }
}
