import sendDataToHubspot from "../../common/sendDataToHubSpot";
import sendDataToSendGrid from "../../common/sendDataToSendGrid";
import currentTimestamp from "../../common/currentTimestamp";
import sendToSlack from "../../common/sendDataToSlack";

export const handler = async (event) => {
  try {
    const form_data = JSON.parse(event.body);

    const formFields = [
      { name: "firstname", value: form_data?.firstName ?? "" },
      { name: "lastname", value: form_data?.lastName ?? "" },
      { name: "email", value: form_data?.emailAddress ?? "" },
      { name: "phone", value: form_data?.phoneNumber ?? "" },
      {
        name: "how_did_you_hear_about_us_",
        value: form_data?.howDidYouHearAboutUs ?? "",
      },
      { name: "company", value: form_data?.companyName },
      { name: "message", value: form_data?.howCanWeHelpYou },
      { name: "utm_source", value: form_data?.utm_source ?? "" },
      { name: "utm_campaign", value: form_data?.utm_campaign ?? "" },
      { name: "utm_medium", value: form_data?.utm_medium ?? "" },
      { name: "clarity_link", value: form_data?.clarity ?? "" },
      { name: "source_url", value: form_data?.url ?? "" },
      { name: "referrer", value: form_data?.referrer ?? "" },
      { name: "ip_address", value: form_data?.ip_address ?? "" },
      { name: "city", value: form_data?.city ?? "" },
      { name: "country", value: form_data?.country ?? "" },
      { name: "ga_4_userid", value: form_data?.ga_4_userid },
      { name: "source", value: form_data?.secondary_source ?? "HomePage" },
      { name: "consent", value: form_data?.consent ?? "" },
    ];

    const payload = {
      fields: formFields,
      context: { pageUri: form_data?.url },
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_HUBSPOT_API_KEY}`,
      },
    };

    try {
      // Send Data to HubSpot
      const hubspotResponse = await sendDataToHubspot(
        form_data?.secondary_source,
        payload,
        process.env.NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID
      );

      if (hubspotResponse?.status === 200 || hubspotResponse?.status === 201) {
        // Send Data to SendGrid if HubSpot submission is successful
        const emailRes = await sendDataToSendGrid(
          process.env.NEXT_PUBLIC_MAIL_TO,
          process.env.NEXT_PUBLIC_MAIL_FROM,
          form_data?.emailAddress,
          process.env.NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID,
          form_data
        );

        // Send Data to success Slack channel
        await sendToSlack(
          form_data,
          process.env.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL
        );

        console.log(currentTimestamp());
        console.log("Lead Data", form_data);
        console.log("HubSpot Response", hubspotResponse);
        console.log("SendGrid Email Response", emailRes);
        console.log("------------------------------------");

        return {
          statusCode: 200,
          body: JSON.stringify({
            message: "Form submitted successfully.",
            hubspotResponse: hubspotResponse.message,
          }),
        };
      } else {
        console.error("HubSpot Error:", hubspotResponse);

        let formLeadData = form_data;
        formLeadData.page_name = form_data?.secondary_source;
        formLeadData.failed_source = "Hubspot";

        const failureEmail = await sendDataToSendGrid(
          process.env.NEXT_PUBLIC_MAIL_TO,
          process.env.NEXT_PUBLIC_MAIL_FROM,
          form_data?.emailAddress,
          process.env.NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID,
          formLeadData
        );

        await sendToSlack(
          form_data,
          process.env.NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL,
          "⚠️ HubSpot Form Submission Failed ⚠️"
        );

        if (failureEmail.status) {
          console.error(
            `${form_data?.secondary_source} form, failure email sent`
          );
        } else {
          console.error(
            `${form_data?.secondary_source} form, failed to send failure email`
          );
        }

        return {
          statusCode: hubspotResponse?.status || 500,
          body: JSON.stringify({
            message: "Form submission failed.",
            error: hubspotResponse?.error || "Unknown error from HubSpot",
          }),
        };
      }
    } catch (error) {
      console.error("Error sending to HubSpot:", error);
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: "Internal server error while sending data to HubSpot",
          error: error.message || error,
        }),
      };
    }
  } catch (error) {
    console.error("Error parsing request:", error);
    return {
      statusCode: 400,
      body: JSON.stringify({
        message: "Invalid request data",
        error: error.message || error,
      }),
    };
  }
};
