name: Deploy Contact Us Lambda

on:
  push:
    branches:
      - main
    paths:
      - 'api/contact-us/**'
      - 'api/common/**'
      - 'package.json'
      - 'package-lock.json'
      - 'tsconfig.json'

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm ci

      - name: Transpile TypeScript (if needed)
        run: npx tsc

      - name: Zip Lambda code
        run: |
          mkdir -p lambda-dist
          cp -r api/contact-us lambda-dist/
          cp -r api/common lambda-dist/
          cp package.json lambda-dist/
          cp -r node_modules lambda-dist/
          cd lambda-dist
          zip -r lambda.zip .

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-south-1  # Change to your region

      - name: Deploy Lambda
        run: |
          aws lambda update-function-code \
            --function-name contact-us-function \
            --zip-file fileb://lambda-dist/lambda.zip
