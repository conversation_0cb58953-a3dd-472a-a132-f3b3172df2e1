@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm-450, breakpoint-xl, breakpoint-xl-1024, breakpoint-md from breakpoints;

.container {
  display: flex;
  max-width: 1192px;
  gap: 20px;

  @media screen and (max-width: breakpoint-xl-1024) {
    flex-direction: column;
  }
}

.question_container {
  display: flex;
  flex-direction: column;
  width: 384px;
  gap: 10px;
  padding: 20px;

  @media screen and (max-width: breakpoint-xl-1024) {
    width: 100%;
  }
}

.question_number {
  font-weight: 600;
  font-size: 32px;
  line-height: 132%;
}

.sub_question_name {
  min-width: 100%;
  font-weight: 500;
  font-size: 26px;
  line-height: 164%;
}

.question_name {
  min-width: 344px;
  font-weight: 500;
  font-size: 26px;
  line-height: 164%;

  @media screen and (max-width: breakpoint-xl-1024) {
    min-width: 100%;
  }
}

.error_message {
  color: #ff0000;
}

.mcqs_container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 20px;
  }
}

.selected_mcq {
  background-image: linear-gradient(gray300, gray300),
    linear-gradient(
      93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%
    );
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;
}

.mcq {
  background-color: gray300;
  width: 384px;
  padding: 20px;
  border-radius: 12px;
  font-weight: 400;
  font-size: 20px;
  line-height: 160%;

  position: relative;
  border: 2px solid transparent;
  cursor: pointer;

  @media screen and (max-width: breakpoint-xl) {
    width: 47%;
  }

  @media screen and (max-width: breakpoint-sm-450) {
    width: 100%;
  }
}

.mcq > input[type='radio'] {
  display: none;
}

.draggable_container {
  width: 788px;
  height: min-content;
  background-color: gray300;
  margin-top: 80px;
  padding: 20px;
  border-radius: 12px;

  @media screen and (max-width: breakpoint-xl) {
    width: 60%;
  }
}

.draggable_input {
  -webkit-appearance: none;
  width: 680px;
  height: 7px;
  background: transparent;
  border-radius: 3px;
  outline: none;
  margin: 20px 30px;
  cursor: pointer;

  @media screen and (max-width: breakpoint-xl) {
    width: 90%;
  }
}

.draggable_input::-webkit-slider-runnable-track,
.draggable_input::-moz-range-track {
  height: 7px;
}

.draggable_input::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background: #30ad43;
  border: none;
  cursor: pointer;
}

.draggable_input::-moz-range-thumb {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background: #30ad43;
  border: none;
  cursor: pointer;
}

.draggable_wrapper {
  display: flex;
  justify-content: space-between;
}

.draggable_label {
  cursor: pointer;
  text-align: center;
  width: 127px;
  height: 44px;
}

.selected_draggable_label {
  background: linear-gradient(
    to right,
    brandColorOne,
    brandColorTwo,
    brandColorThree,
    brandColorFour,
    brandColorFive
  );
  background-clip: text;
  color: transparent;
}

.number_wrapper {
  display: flex;
  flex-direction: column;
}

.draggable_container_tablet {
  display: flex;
  flex-direction: row;
  padding: 20px;
}

.number {
  width: 20%;
  height: 62px;
  padding: 10px;
  border: 0.5px solid #8c8b8b;

  font-weight: 500;
  font-size: 26px;
  line-height: 164%;
  text-align: center;
}

.number:first-child {
  border-radius: 10px 0 0 10px;
}

.number:last-child {
  border-radius: 0 10px 10px 0;
}

.number_label {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}

.number_label > span {
  text-align: center;
  width: 20%;
}

.selected_number {
  background-color: #30ad43;
}

.number > input[type='radio'] {
  display: none;
}
