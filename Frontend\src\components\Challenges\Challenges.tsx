'use client';

import React from 'react';
import { Container } from 'react-bootstrap';
import Image from 'next/image';
import Heading from '@components/Heading';
import { EmblaOptionsType } from 'embla-carousel';
import useDotButton from '@hooks/useDotButton';
import DotButton from '@components/DotButton/DotButton';
import styles from './Challenges.module.css';
import useEmblaCarousel from 'embla-carousel-react';
import classNames from '@utils/classNames';

import emblastyles from '../../styles/emlaDots.module.css';

export default function Challenges({ dataChallenges, variantWhite = false }) {
  const OPTIONS: EmblaOptionsType = {
    align: 'start',
    dragFree: true,
  };

  const [emblaRef, emblaApi] = useEmblaCarousel(OPTIONS);
  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  return (
    <>
      <Container fluid className={styles.container}>
        <div className={styles.title_desc_section}>
          <Heading
            headingType="h2"
            title={dataChallenges?.title}
            className={styles.title}
          />
          <div
            className={styles.description}
            dangerouslySetInnerHTML={{ __html: dataChallenges?.description }}
          ></div>
        </div>
        <div className={styles.embla}>
          <div className={styles.embla__viewport} ref={emblaRef}>
            <div className={styles.embla__container}>
              {dataChallenges?.challenges_box?.map((data, index) => (
                <div className={styles.embla__slide} key={index}>
                  <div
                    className={classNames(
                      styles.embla__slide__number,
                      index % 2 !== 0 && styles.columnReverse,
                    )}
                    key={index}
                  >
                    <div className={styles.box}>
                      <Heading
                        headingType="h3"
                        title={data?.title}
                        className={styles.box_title}
                      />
                      <div
                        className={styles.box_description}
                        dangerouslySetInnerHTML={{
                          __html: data?.description,
                        }}
                      ></div>
                    </div>
                    <Image
                      src={data?.image?.data?.attributes?.url}
                      alt={data?.image?.data?.attributes?.alternativeText}
                      width={270}
                      height={269}
                      className={styles.image}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className={styles.embla__controls}>
            <div className={emblastyles.embla__dots}>
              {scrollSnaps.length > 1 &&
                scrollSnaps.map((_, index) => (
                  <DotButton
                    key={index}
                    onClick={() => onDotButtonClick(index)}
                    className={
                      index === selectedIndex
                        ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                        : variantWhite
                          ? classNames(
                              emblastyles.embla__dot,
                              emblastyles.embla__dot_bg_white,
                            )
                          : emblastyles.embla__dot
                    }
                  />
                ))}
            </div>
          </div>
        </div>
      </Container>
    </>
  );
}
