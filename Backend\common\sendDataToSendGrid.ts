import axios from "axios";
import { getBackendConfigValue } from "../config/environment";

const sendDataToSendGrid = async (
  to,
  from,
  replyTo,
  templateId,
  dynamicData
) => {
  // Get SendGrid API key from configuration
  const sendgridApiKey = getBackendConfigValue('SENDGRID_API_KEY');

  if (!sendgridApiKey) {
    throw new Error('SendGrid API key not configured');
  }

  let config = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + sendgridApiKey,
    },
  };

  // Get second recipient from configuration
  const secondRecipient = getBackendConfigValue('SECOND_RECIPIENT');

  let mailTo = secondRecipient
    ? [
      {
        email: to,
      },
      {
        email: secondRecipient,
      },
    ]
    : [
      {
        email: to,
      },
    ];

  const data = {
    from: {
      email: from,
    },
    reply_to: {
      email: replyTo,
    },
    personalizations: [
      {
        to: mailTo,
        dynamic_template_data: {
          lead: dynamicData,
        },
      },
    ],
    template_id: templateId,
  };

  try {
    let result = await axios.post(
      "https://api.sendgrid.com/v3/mail/send",
      data,
      config
    );
    if (result.status === 200 || result.status === 202) {
      return {
        status: true,
        message: "Sendgrid API message: Email sent successfully.",
      };
    } else {
      throw "Error";
    }
  } catch (error) {
    console.error("--------error", error);
    return {
      status: false,
      error: `Sendgrid API message: Error while sending email.`,
    };
  }
};

export default sendDataToSendGrid;
