name: Deploy Static Next.js Site to S3 with CloudFront Invalidation

on:
  push:
    branches:
      - development
    paths:
      - Frontend/**

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      # ----------------------------------------
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: 🔧 Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-south-1

      - name: 🔑 Fetch env vars from SSM Parameter Store
        # shell: bash -euo pipefail
        run: |
          aws ssm get-parameters-by-path \
            --recursive \
            --path "/maruti_site/env/" \
            --with-decryption \
            --query 'Parameters[*].[Name,Value]' \
            --output text |
          while IFS=$'\t' read -r name value; do
            key=$(basename "$name")
            clean=$(printf '%s' "$value" | tr -d '\r\n')
            echo "::add-mask::$clean"
             echo "${key}=${clean}" >> $GITHUB_ENV
          done

      # ----------------------------------------
      - name: 📦 Install Dependencies
        run: npm install
        working-directory: Frontend

      - name: 🏗️ Build and Export Static Site
        run: npm run build          # NEXT_PUBLIC_* vars available here
        working-directory: Frontend

      # ----------------------------------------
      - name: ☁️ Upload build to S3 using AWS CLI
        run: |
           aws s3 sync out s3://mtl-site-dev-environment --delete
        working-directory: Frontend
        env:
          AWS_ACCESS_KEY_ID:        ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY:    ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION:               ap-south-1

      - name: 🚀 Invalidate CloudFront Cache
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ secrets.cloudfront_id }} \
            --paths "/*"
        env:
          AWS_ACCESS_KEY_ID:        ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY:    ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION:               ap-south-1
